// UTComp_HudCommon.uci - Common HUD functions for all UTComp HUD classes
// Include this file in UTComp HUD classes to eliminate code duplication

simulated function UpdatePrecacheMaterials()
{
	local int i;

	for (i = 0; i < class'UTComp_HudSettings'.default.UTCompCrosshairs.Length && class'UTComp_HudSettings'.default.bEnableUTCompCrosshairs; i++)
		Level.AddPrecacheMaterial(class'UTComp_HudSettings'.default.UTCompCrosshairs[i].CrossTex);

	Super.UpdatePrecacheMaterials();
}

exec function NextStats()
{
	if (ScoreBoard == none || !bShowScoreBoard)
		Super.NextStats();
	else
		ScoreBoard.NextStats();
}

function DisplayEnemyName(Canvas C, PlayerReplicationInfo PRI)
{
	PlayerOwner.ReceiveLocalizedMessage(class'BTM_PlayerName', 0, PRI);
}

simulated function DrawUTCompCrosshair(Canvas C)
{
	local int i;
	local float OldScale, OldW;
	local array<SpriteWidget> CHTexture;

	if (PawnOwner.bSpecialCrosshair)
	{
		PawnOwner.SpecialDrawCrosshair(C);
		return;
	}

	if (!bCrosshairShow)
		return;

	for (i = 0; i < class'UTComp_HudSettings'.default.UTCompCrosshairs.Length; i++)
	{
		CHTexture.Length = i + 1;
		CHTexture[i].WidgetTexture = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].CrossTex;
		CHTexture[i].RenderStyle = STY_Alpha;
		CHTexture[i].TextureCoords.X2 = 64;
		CHTexture[i].TextureCoords.Y2 = 64;
		CHTexture[i].TextureScale = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].CrossScale * 0.50;
		CHTexture[i].DrawPivot = DP_MiddleMiddle;
		CHTexture[i].PosX = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].OffsetX;
		CHTexture[i].PosY = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].OffsetY;
		CHTexture[i].ScaleMode = SM_None;
		CHTexture[i].Scale = 1.00;
		CHTexture[i].Tints[0] = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].CrossColor;
		CHTexture[i].Tints[1] = class'UTComp_HudSettings'.default.UTCompCrosshairs[i].CrossColor;
	}

	if (class'UTComp_HudSettings'.default.bEnableCrosshairSizing && LastPickupTime > Level.TimeSeconds - 0.4)
	{
		if (LastPickupTime > Level.TimeSeconds - 0.2)
			for (i = 0; i < CHTexture.Length; i++)
				CHTexture[i].TextureScale *= (1 + 5 * (Level.TimeSeconds - LastPickupTime));
		else
			for (i = 0; i < CHTexture.Length; i++)
				CHTexture[i].TextureScale *= (1 + 5 * (LastPickupTime + 0.4 - Level.TimeSeconds));
	}

	OldScale = HudScale;
	HudScale = 1;
	OldW = C.ColorModulate.W;
	C.ColorModulate.W = 1;
	for (i = 0; i < CHTexture.Length; i++)
		DrawSpriteWidget(C, CHTexture[i]);
	C.ColorModulate.W = OldW;
	HudScale = OldScale;

	DrawEnemyName(C);
}

simulated function DrawCrosshair(Canvas C)
{
	if (class'UTComp_HudSettings'.default.bEnableUTCompCrosshairs && class'UTComp_HudSettings'.default.UTCompCrosshairs.Length > 0)
		DrawUTCompCrosshair(C);
	else
		OldDrawCrosshair(C);
}

simulated function OldDrawCrosshair(Canvas C)
{
	local int i, CurrentCrosshair;
	local float NormalScale, OldScale, OldW, CurrentCrosshairScale;
	local color CurrentCrosshairColor;
	local SpriteWidget CHTexture;

	if (PawnOwner.bSpecialCrosshair)
	{
		PawnOwner.SpecialDrawCrosshair(C);
		return;
	}

	if (!bCrosshairShow)
		return;

	if (bUseCustomWeaponCrosshairs && (PawnOwner != none) && (PawnOwner.Weapon != none))
	{
		CurrentCrosshair = PawnOwner.Weapon.CustomCrosshair;
		if (CurrentCrosshair == -1 || CurrentCrosshair == Crosshairs.Length)
		{
			CurrentCrosshair = CrosshairStyle;
			CurrentCrosshairColor = CrosshairColor;
			CurrentCrosshairScale = CrosshairScale;
		}
		else
		{
			CurrentCrosshairColor = PawnOwner.Weapon.CustomCrosshairColor;
			CurrentCrosshairScale = PawnOwner.Weapon.CustomCrosshairScale;
			if (PawnOwner.Weapon.CustomCrosshairTextureName != "")
			{
				if (PawnOwner.Weapon.CustomCrosshairTexture == none)
				{
					PawnOwner.Weapon.CustomCrosshairTexture = Texture(DynamicLoadObject(PawnOwner.Weapon.CustomCrosshairTextureName, class'Texture'));
					if (PawnOwner.Weapon.CustomCrosshairTexture == none)
					{
						Log(PawnOwner.Weapon $ " custom crosshair texture not found!");
						PawnOwner.Weapon.CustomCrosshairTextureName = "";
					}
				}
				CHTexture = Crosshairs[0];
				CHTexture.WidgetTexture = PawnOwner.Weapon.CustomCrosshairTexture;
			}
		}
	}
	else
	{
		CurrentCrosshair = CrosshairStyle;
		CurrentCrosshairColor = CrosshairColor;
		CurrentCrosshairScale = CrosshairScale;
	}

	CurrentCrosshair = Clamp(CurrentCrosshair, 0, Crosshairs.Length - 1);

	NormalScale = Crosshairs[CurrentCrosshair].TextureScale;
	if (CHTexture.WidgetTexture == none)
		CHTexture = Crosshairs[CurrentCrosshair];
	CHTexture.TextureScale *= 0.5 * CurrentCrosshairScale;

	for (i = 0; i < ArrayCount(CHTexture.Tints); i++)
		CHTexture.Tints[i] = CurrentCrosshairColor;

	if (class'UTComp_HudSettings'.default.bEnableCrosshairSizing && LastPickupTime > Level.TimeSeconds - 0.4)
	{
		if (LastPickupTime > Level.TimeSeconds - 0.2)
			CHTexture.TextureScale *= (1 + 5 * (Level.TimeSeconds - LastPickupTime));
		else
			CHTexture.TextureScale *= (1 + 5 * (LastPickupTime + 0.4 - Level.TimeSeconds));
	}

	OldScale = HudScale;
	HudScale = 1;
	OldW = C.ColorModulate.W;
	C.ColorModulate.W = 1;
	DrawSpriteWidget(C, CHTexture);
	C.ColorModulate.W = OldW;
	HudScale = OldScale;
	CHTexture.TextureScale = NormalScale;

	DrawEnemyName(C);
}

simulated function DrawTimer(Canvas C)
{
	local int Minutes, Hours, Seconds;
	local GameReplicationInfo GRI;
	local BTRI_Warmup uWarmup;

	GRI = PlayerOwner.GameReplicationInfo;
	if (BS_xPlayer(PlayerOwner) != none)
	{
		if (BS_xPlayer(PlayerOwner).uWarmup != none)
			uWarmup = BS_xPlayer(PlayerOwner).uWarmup;
	}

	if (GRI.TimeLimit == 0)
		Seconds = GRI.ElapsedTime;
	else if (GRI.RemainingTime > 0 || GRI.ElapsedTime < 60 || (uWarmup != none && uWarmup.bInWarmup))
		Seconds = GRI.RemainingTime;
	else
		Seconds = GRI.ElapsedTime - GRI.TimeLimit * 60 - 1;

	TimerBackground.Tints[TeamIndex] = HudColorBlack;
	TimerBackground.Tints[TeamIndex].A = 150;

	DrawSpriteWidget(C, TimerBackground);
	DrawSpriteWidget(C, TimerBackgroundDisc);
	DrawSpriteWidget(C, TimerIcon);

	TimerMinutes.OffsetX = default.TimerMinutes.OffsetX - 80;
	TimerSeconds.OffsetX = default.TimerSeconds.OffsetX - 80;
	TimerDigitSpacer[0].OffsetX = default.TimerDigitSpacer[0].OffsetX;
	TimerDigitSpacer[1].OffsetX = default.TimerDigitSpacer[1].OffsetX;

	if (Seconds > 3600)
	{
		Hours = Seconds / 3600;
		Seconds -= Hours * 3600;

		DrawNumericWidget(C, TimerHours, DigitsBig);
		TimerHours.Value = Hours;

		if (Hours > 9)
		{
			TimerMinutes.OffsetX = default.TimerMinutes.OffsetX;
			TimerSeconds.OffsetX = default.TimerSeconds.OffsetX;
		}
		else
		{
			TimerMinutes.OffsetX = default.TimerMinutes.OffsetX - 40;
			TimerSeconds.OffsetX = default.TimerSeconds.OffsetX - 40;
			TimerDigitSpacer[0].OffsetX = default.TimerDigitSpacer[0].OffsetX - 32;
			TimerDigitSpacer[1].OffsetX = default.TimerDigitSpacer[1].OffsetX - 32;
		}
		DrawSpriteWidget(C, TimerDigitSpacer[0]);
	}
	DrawSpriteWidget(C, TimerDigitSpacer[1]);

	Minutes = Seconds / 60;
	Seconds -= Minutes * 60;

	TimerMinutes.Value = Min(Minutes, 60);
	TimerSeconds.Value = Min(Seconds, 60);

	DrawNumericWidget(C, TimerMinutes, DigitsBig);
	DrawNumericWidget(C, TimerSeconds, DigitsBig);
}
